{"name": "solidity-next-js-starter-contracts", "version": "2.1.0", "description": "A basic contract for starting to build full stack Ethereum dApps", "main": "index.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"start": "hardhat node", "test": "hardhat test", "deploy": "hardhat run --network localhost scripts/deploy.js", "deploy:sepolia": "hardhat run --network sepolia scripts/deploy.js", "verify:sepolia": "hardhat verify --network sepolia", "format": "prettier --write \"./contracts/**/*.sol\" --plugin prettier-plugin-solidity && prettier --write \"./**/*.ts\""}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.2", "@nomicfoundation/hardhat-ethers": "^3.0.4", "@nomicfoundation/hardhat-network-helpers": "^1.0.9", "@nomicfoundation/hardhat-toolbox": "^3.0.0", "@nomicfoundation/hardhat-verify": "^1.1.1", "@typechain/ethers-v6": "^0.4.0", "@typechain/hardhat": "^8.0.0", "chai": "^4.3.8", "ethers": "^6.15.0", "hardhat": "^2.25.0", "hardhat-gas-reporter": "^1.0.9", "prettier": "^3.0.3", "prettier-plugin-solidity": "^1.1.3", "solidity-coverage": "^0.8.4", "typechain": "^8.3.1", "typescript": "^5.8.3"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0", "dotenv": "^16.3.1", "ts-node": "^10.9.2"}}