{"name": "listener", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "bun src/index.js", "dev": "nodemon src/index.js", "postinstall": "prisma generate"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@hono/node-server": "^1.15.0", "@prisma/client": "^6.11.1", "ethers": "^6.15.0", "events": "^3.3.0", "hono": "^4.8.4"}, "devDependencies": {"prisma": "^6.11.1"}}