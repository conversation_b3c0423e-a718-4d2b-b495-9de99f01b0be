'use client'

import React from 'react'

function UserPage({ user: rawUser }) {

  const [user, setUser] = React.useState();

  React.useEffect(() => {
    // Parse user once and set the state
    if (rawUser) {
      setUser(JSON.parse(rawUser));
      // console.log("User:", JSON.parse(rawUser)); // Keep this for debugging if needed
    }
  }, [rawUser]);

  if (!user) {
    return (
      <div>Loading user...</div>
    )
  }
  
  return (
    <div>UserPage</div>
  )
}

export default UserPage