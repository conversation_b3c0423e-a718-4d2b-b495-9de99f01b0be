{"name": "chario", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@rainbow-me/rainbowkit": "^2.2.8", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "better-auth": "^1.2.12", "bijective-varint": "^1.2.0", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "ethers": "^6.15.0", "hashids": "^2.3.0", "input-otp": "^1.4.2", "lucide-react": "^0.525.0", "motion": "^12.23.0", "nanoid": "^5.1.5", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-phone-number-input": "^3.4.12", "recharts": "^2.15.4", "resend": "^4.6.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "validation-better-auth": "^1.3.4", "viem": "^2.31.7", "wagmi": "^2.15.6", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.5", "prisma": "^6.11.1", "tailwindcss": "^4"}}