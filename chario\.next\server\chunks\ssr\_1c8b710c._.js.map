{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/home/<USER>/%5BuserId%5D/_components/user-page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\n\r\nfunction UserPage({ user: rawUser }) {\r\n\r\n  const [user, setUser] = React.useState();\r\n\r\n  React.useEffect(() => {\r\n    // Parse user once and set the state\r\n    if (rawUser) {\r\n      setUser(JSON.parse(rawUser));\r\n      // console.log(\"User:\", JSON.parse(rawUser)); // Keep this for debugging if needed\r\n    }\r\n  }, [rawUser]);\r\n\r\n  if (!user) {\r\n    return (\r\n      <div>Loading user...</div>\r\n    )\r\n  }\r\n  \r\n  return (\r\n    <div>UserPage</div>\r\n  )\r\n}\r\n\r\nexport default UserPage"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,SAAS,SAAS,EAAE,MAAM,OAAO,EAAE;IAEjC,MAAM,CAAC,MAAM,QAAQ,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ;IAEtC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,oCAAoC;QACpC,IAAI,SAAS;YACX,QAAQ,KAAK,KAAK,CAAC;QACnB,kFAAkF;QACpF;IACF,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;sBAAI;;;;;;IAET;IAEA,qBACE,8OAAC;kBAAI;;;;;;AAET;uCAEe", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/not-found.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport Link from 'next/link'\r\nimport { CopyXIcon } from 'lucide-react'\r\n\r\nfunction NotFound({ title, description }) {\r\n    return (\r\n        <div className='h-full w-full flex flex-col items-center justify-center p-8 text-center'>\r\n            <CopyXIcon size={40} className='text-primary mb-6' />\r\n            <h1 className='font-bold text-2xl mb-1'>{title}</h1>\r\n            <p className='text-muted-foreground text-xs mb-8'>{description}</p>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default NotFound"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,SAAS,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE;IACpC,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC,4MAAA,CAAA,YAAS;gBAAC,MAAM;gBAAI,WAAU;;;;;;0BAC/B,8OAAC;gBAAG,WAAU;0BAA2B;;;;;;0BACzC,8OAAC;gBAAE,WAAU;0BAAsC;;;;;;;;;;;;AAG/D;uCAEe", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "file": "copy-x.js", "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/node_modules/lucide-react/src/icons/copy-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '18', y1: '12', y2: '18', key: '1rg63v' }],\n  ['line', { x1: '12', x2: '18', y1: '18', y2: '12', key: 'ebkxgr' }],\n  ['rect', { width: '14', height: '14', x: '8', y: '8', rx: '2', ry: '2', key: '17jyea' }],\n  ['path', { d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2', key: 'zix9uf' }],\n];\n\n/**\n * @component @name CopyX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxOCIgeTE9IjEyIiB5Mj0iMTgiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTgiIHkxPSIxOCIgeTI9IjEyIiAvPgogIDxyZWN0IHdpZHRoPSIxNCIgaGVpZ2h0PSIxNCIgeD0iOCIgeT0iOCIgcng9IjIiIHJ5PSIyIiAvPgogIDxwYXRoIGQ9Ik00IDE2Yy0xLjEgMC0yLS45LTItMlY0YzAtMS4xLjktMiAyLTJoMTBjMS4xIDAgMiAuOSAyIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/copy-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CopyX = createLucideIcon('copy-x', __iconNode);\n\nexport default CopyX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA2D,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC1F;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}