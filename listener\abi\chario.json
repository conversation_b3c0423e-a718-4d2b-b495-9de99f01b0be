[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "charityId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "string", "name": "title", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "target", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "deadline", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "image", "type": "string"}, {"indexed": false, "internalType": "string", "name": "userId", "type": "string"}], "name": "CharityCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "charityId", "type": "uint256"}, {"indexed": false, "internalType": "enum Chario.CharityStatus", "name": "newStatus", "type": "uint8"}], "name": "CharityStatusUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "charityId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "donor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "userId", "type": "string"}], "name": "DonationReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "userId", "type": "string"}, {"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}], "name": "WalletLinked", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "charities", "outputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "string", "name": "title", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "target", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint256", "name": "amountCollected", "type": "uint256"}, {"internalType": "string", "name": "image", "type": "string"}, {"internalType": "enum Chario.CharityStatus", "name": "status", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "string", "name": "_title", "type": "string"}, {"internalType": "string", "name": "_description", "type": "string"}, {"internalType": "uint256", "name": "_target", "type": "uint256"}, {"internalType": "uint256", "name": "_deadline", "type": "uint256"}, {"internalType": "string", "name": "_image", "type": "string"}, {"internalType": "string", "name": "_userId", "type": "string"}], "name": "createCharity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "string", "name": "_userId", "type": "string"}], "name": "donateToCharity", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "donorContributions", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "getCharity", "outputs": [{"components": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "string", "name": "title", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "target", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint256", "name": "amountCollected", "type": "uint256"}, {"internalType": "string", "name": "image", "type": "string"}, {"internalType": "enum Chario.CharityStatus", "name": "status", "type": "uint8"}], "internalType": "struct Chario.Charity", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "address", "name": "donor", "type": "address"}], "name": "getDonorContribution", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "wallet", "type": "address"}], "name": "getUserByWallet", "outputs": [{"components": [{"internalType": "uint8", "name": "verifiedLevel", "type": "uint8"}, {"internalType": "uint256[]", "name": "badgeTokenIds", "type": "uint256[]"}, {"internalType": "address[]", "name": "linkedWallets", "type": "address[]"}], "internalType": "struct Chario.User", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "userId", "type": "string"}, {"internalType": "address", "name": "wallet", "type": "address"}], "name": "linkWalletToUser", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "numberOfCharities", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "enum Chario.CharityStatus", "name": "_newStatus", "type": "uint8"}], "name": "setCharityStatus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "userId", "type": "string"}, {"internalType": "uint8", "name": "level", "type": "uint8"}], "name": "setUserVerifiedLevel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "", "type": "string"}], "name": "users", "outputs": [{"internalType": "uint8", "name": "verifiedLevel", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "walletToUserId", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}]