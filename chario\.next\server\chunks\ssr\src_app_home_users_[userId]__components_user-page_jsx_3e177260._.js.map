{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/home/<USER>/%5BuserId%5D/_components/user-page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\n\r\nfunction UserPage() {\r\n  return (\r\n    <div>UserPage</div>\r\n  )\r\n}\r\n\r\nexport default UserPage"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,SAAS;IACP,qBACE,8OAAC;kBAAI;;;;;;AAET;uCAEe", "debugId": null}}]}