{"version": 3, "sources": [], "sections": [{"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/hooks/use-chario.js"], "sourcesContent": ["// \"use client\";\r\n\r\n// import { useState, useCallback, useEffect } from 'react';\r\n// import {\r\n//     useAccount,\r\n//     useReadContract,\r\n//     useWriteContract,\r\n//     useWaitForTransactionReceipt,\r\n// } from \"wagmi\";\r\n// import abi from \"@/abi/chario.json\"; // Update to your contract's ABI\r\n// import { parseEther, formatEther } from 'viem';\r\n// import { toast } from \"sonner\";\r\n\r\n// const CHARIO_ABI = abi\r\n// const CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_CONTRACT_ADDRESS\r\n\r\n// export const useChario = () => {\r\n//     const { address, isConnected } = useAccount();\r\n//     const [isLoading, setIsLoading] = useState(false);\r\n//     const [pendingTxHash, setPendingTxHash] = useState(null);\r\n\r\n//     // Write contract hook\r\n//     const { writeContract, data: txHash, error: writeError, isPending: isWritePending } = useWriteContract();\r\n\r\n//     // Wait for transaction receipt\r\n//     const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({\r\n//         hash: txHash,\r\n//     });\r\n\r\n//     // Handle transaction completion\r\n//     useEffect(() => {\r\n//         if (txHash) {\r\n//             console.log('Transaction Hash:', txHash);\r\n//         }\r\n//         if (isConfirmed && pendingTxHash) {\r\n//             toast.success('Transaction confirmed!');\r\n//             setIsLoading(false);\r\n//             setPendingTxHash(null);\r\n//         }\r\n//     }, [isConfirmed, pendingTxHash, txHash]); // Add txHash to dependency array\r\n\r\n//     useEffect(() => {\r\n//         if (writeError) {\r\n//             console.error('Wagmi Write Error:', writeError);\r\n//             toast.error('Transaction failed: ' + writeError.message);\r\n//             setIsLoading(false);\r\n//             setPendingTxHash(null);\r\n//         }\r\n//     }, [writeError]);\r\n\r\n//     // Read contract data\r\n//     const { data: numberOfCharities, refetch: refetchCharityCount } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'numberOfCharities',\r\n//     });\r\n\r\n//     const { data: userData, refetch: refetchUserData } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getUserByWallet',\r\n//         args: [address],\r\n//         query: {\r\n//             enabled: !!address,\r\n//         },\r\n//     });\r\n\r\n//     // Helper function to execute contract writes\r\n//     const executeWrite = useCallback(async (functionName, args, value, successMessage) => {\r\n//         if (!isConnected) {\r\n//             toast.error('Please connect your wallet');\r\n//             return;\r\n//         }\r\n\r\n//         setIsLoading(true);\r\n\r\n//         try {\r\n//             const hash = await writeContract({\r\n//                 address: CONTRACT_ADDRESS,\r\n//                 abi: CHARIO_ABI,\r\n//                 functionName: functionName,\r\n//                 args: args,\r\n//                 value: value,\r\n//             });\r\n\r\n//             setPendingTxHash(hash);\r\n//             toast.success(successMessage || 'Transaction submitted');\r\n//         } catch (error) {\r\n//             console.error(`Error executing ${functionName}:`, error);\r\n//             toast.error(error.message || 'Transaction failed');\r\n//             setIsLoading(false);\r\n//         }\r\n//     }, [writeContract, isConnected]);\r\n\r\n//     // Create Charity\r\n//     const createCharity = useCallback(async (params) => {\r\n//         const { owner, title, description, target, deadline, image } = params;\r\n\r\n//         try {\r\n//             const targetInWei = parseEther(target.toString());\r\n//             await executeWrite(\r\n//                 'createCharity',\r\n//                 [owner, title, description, targetInWei, deadline, image],\r\n//                 undefined,\r\n//                 'Creating charity...'\r\n//             );\r\n//         } catch (error) {\r\n//             console.error('Error creating charity:', error);\r\n//             toast.error('Failed to create charity');\r\n//         }\r\n//     }, [executeWrite]);\r\n\r\n//     // Donate to Charity\r\n//     const donateToCharity = useCallback(async (charityId, amount) => {\r\n//         try {\r\n//             const amountInWei = parseEther(amount.toString());\r\n//             await executeWrite(\r\n//                 'donateToCharity',\r\n//                 [charityId],\r\n//                 amountInWei,\r\n//                 'Processing donation...'\r\n//             );\r\n//         } catch (error) {\r\n//             console.error('Error donating:', error);\r\n//             toast.error('Failed to donate');\r\n//         }\r\n//     }, [executeWrite]);\r\n\r\n//     // Withdraw Funds\r\n//     const withdrawFunds = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'withdrawFunds',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Withdrawing funds...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Refund Donation\r\n//     const refundDonation = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'refundDonation',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Processing refund...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Cancel Charity\r\n//     const cancelCharity = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'cancelCharity',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Cancelling charity...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     // Update Charity Status\r\n//     const updateCharityStatus = useCallback(async (charityId) => {\r\n//         await executeWrite(\r\n//             'updateCharityStatus',\r\n//             [charityId],\r\n//             undefined,\r\n//             'Updating status...'\r\n//         );\r\n//     }, [executeWrite]);\r\n\r\n//     const loading = isLoading || isWritePending || isConfirming;\r\n\r\n//     return {\r\n//         // Data\r\n//         numberOfCharities,\r\n//         userData,\r\n\r\n//         // Actions\r\n//         createCharity,\r\n//         donateToCharity,\r\n//         withdrawFunds,\r\n//         refundDonation,\r\n//         cancelCharity,\r\n//         updateCharityStatus,\r\n\r\n//         // Refetch functions\r\n//         refetchCharityCount,\r\n//         refetchUserData,\r\n\r\n//         // State\r\n//         loading,\r\n//         isConnected,\r\n//         address,\r\n//         txHash,\r\n\r\n//         // Utilities\r\n//         formatEther,\r\n//         parseEther,\r\n//     };\r\n// };\r\n\r\n// // Hook for fetching multiple charities\r\n// export const useCharities = (limit) => {\r\n//     const [charities, setCharities] = useState([]);\r\n//     const [isLoading, setIsLoading] = useState(false);\r\n\r\n//     const { data: numberOfCharities } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'numberOfCharities',\r\n//     });\r\n\r\n//     const totalCharities = numberOfCharities ? Number(numberOfCharities) : 0;\r\n//     const charityLimit = limit ? Math.min(limit, totalCharities) : totalCharities;\r\n\r\n//     // Fetch all charities\r\n//     useEffect(() => {\r\n//         if (totalCharities > 0) {\r\n//             setIsLoading(true);\r\n//             const fetchCharities = async () => {\r\n//                 const charityPromises = [];\r\n\r\n//                 for (let i = 0; i < charityLimit; i++) {\r\n//                     charityPromises.push(\r\n//                         fetch(`/api/v1/charity/${i}`).then(res => res.json()) // You'll need to implement this API endpoint\r\n//                     );\r\n//                 }\r\n\r\n//                 try {\r\n//                     const results = await Promise.all(charityPromises);\r\n//                     setCharities(results.map((charity, index) => ({ ...charity, id: index })));\r\n//                 } catch (error) {\r\n//                     console.error('Error fetching charities:', error);\r\n//                 } finally {\r\n//                     setIsLoading(false);\r\n//                 }\r\n//             };\r\n\r\n//             fetchCharities();\r\n//         }\r\n//     }, [totalCharities, charityLimit]);\r\n\r\n//     return {\r\n//         charities,\r\n//         isLoading,\r\n//         totalCharities,\r\n//     };\r\n// };\r\n\r\n// // Hook for a single charity\r\n// export const useCharity = (charityId) => {\r\n//     const { address } = useAccount();\r\n\r\n//     const { data: charity, isLoading: isLoadingCharity, refetch: refetchCharity } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getCharity',\r\n//         args: [charityId],\r\n//     });\r\n\r\n//     const { data: donorContribution, isLoading: isLoadingContribution, refetch: refetchContribution } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getDonorContribution',\r\n//         args: [charityId, address],\r\n//         query: {\r\n//             enabled: !!address,\r\n//         },\r\n//     });\r\n\r\n//     const { data: escrowBalance, isLoading: isLoadingEscrow, refetch: refetchEscrow } = useReadContract({\r\n//         address: CONTRACT_ADDRESS,\r\n//         abi: CHARIO_ABI,\r\n//         functionName: 'getEscrowBalance',\r\n//         args: [charityId],\r\n//     });\r\n\r\n//     const isLoading = isLoadingCharity || isLoadingContribution || isLoadingEscrow;\r\n//     const isOwner = (charity?.owner === address);\r\n//     const canRefund = donorContribution && donorContribution > 0n;\r\n\r\n//     // Refetch all charity data\r\n//     const refetchAll = useCallback(() => {\r\n//         refetchCharity();\r\n//         refetchContribution();\r\n//         refetchEscrow();\r\n//     }, [refetchCharity, refetchContribution, refetchEscrow]);\r\n\r\n//     return {\r\n//         charity,\r\n//         donorContribution,\r\n//         escrowBalance,\r\n//         isLoading,\r\n//         isOwner,\r\n//         canRefund,\r\n//         refetchAll,\r\n//     };\r\n// };\r\n\r\n// // Utility hook for charity status\r\n// export const useCharityStatus = (status) => {\r\n//     const statusMap = {\r\n//         0: 'Active',\r\n//         1: 'Inactive',\r\n//         2: 'Completed',\r\n//         3: 'Cancelled'\r\n//     };\r\n\r\n//     const statusColors = {\r\n//         0: 'green',\r\n//         1: 'yellow',\r\n//         2: 'blue',\r\n//         3: 'red'\r\n//     };\r\n\r\n//     return {\r\n//         statusText: statusMap[status] || 'Unknown',\r\n//         statusColor: statusColors[status] || 'gray',\r\n//         isActive: status === 0,\r\n//         isCompleted: status === 2,\r\n//         isCancelled: status === 3,\r\n//         isInactive: status === 1,\r\n//     };\r\n// };\r\n\r\n// // Utility hook for formatting dates\r\n// export const useCharityDates = (deadline) => {\r\n//     const deadlineDate = new Date(deadline * 1000);\r\n//     const now = new Date();\r\n//     const timeLeft = deadlineDate - now;\r\n\r\n//     const isExpired = timeLeft <= 0;\r\n//     const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));\r\n\r\n//     return {\r\n//         deadlineDate,\r\n//         isExpired,\r\n//         daysLeft: isExpired ? 0 : daysLeft,\r\n//         formattedDeadline: deadlineDate.toLocaleDateString(),\r\n//         timeLeftText: isExpired ? 'Expired' : `${daysLeft} days left`,\r\n//     };\r\n// };\r\n\r\n\r\n\"use client\";\r\n\r\nimport { useState, useCallback, useEffect } from 'react';\r\nimport {\r\n    useAccount,\r\n    useReadContract,\r\n    useWriteContract,\r\n    useWaitForTransactionReceipt,\r\n} from \"wagmi\";\r\nimport abi from \"@/abi/chario.json\"; // Make sure this ABI is from your LATEST compiled contract\r\nimport { parseEther, formatEther } from 'viem';\r\nimport { toast } from \"sonner\";\r\n\r\n// --- Configuration ---\r\nconst CHARIO_ABI = abi;\r\nconst CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_CONTRACT_ADDRESS;\r\n\r\n// --- Main Hook for Contract Interactions ---\r\nexport const useChario = () => {\r\n    const { address, isConnected } = useAccount();\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    // Wagmi hooks for writing transactions\r\n    const { data: txHash, writeContractAsync, error: writeError, isPending: isWritePending } = useWriteContract();\r\n\r\n    // Wagmi hook to wait for transaction confirmation\r\n    const { isLoading: isConfirming, isSuccess: isConfirmed } = useWaitForTransactionReceipt({ hash: txHash });\r\n\r\n    // Effect for handling transaction success\r\n    useEffect(() => {\r\n        if (isConfirmed) {\r\n            toast.success('Transaction confirmed!');\r\n            setIsLoading(false);\r\n        }\r\n    }, [isConfirmed]);\r\n\r\n    // Effect for handling transaction errors\r\n    useEffect(() => {\r\n        if (writeError) {\r\n            toast.error(writeError.shortMessage || 'Transaction failed.');\r\n            console.error('Wagmi Write Error:', writeError);\r\n            setIsLoading(false);\r\n        }\r\n    }, [writeError]);\r\n\r\n    // Read contract data\r\n    const { data: numberOfCharities, refetch: refetchCharityCount } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'numberOfCharities',\r\n    });\r\n\r\n    // Helper function to execute contract writes\r\n    const executeWrite = useCallback(async (functionName, args, value, loadingMessage) => {\r\n        if (!isConnected) {\r\n            return {\r\n                error: 'Please connect your wallet first.',\r\n                walletError: true\r\n            };\r\n        }\r\n        setIsLoading(true);\r\n        toast.info(loadingMessage || 'Submitting transaction...');\r\n\r\n        try {\r\n            await writeContractAsync({\r\n                address: CONTRACT_ADDRESS,\r\n                abi: CHARIO_ABI,\r\n                functionName,\r\n                args,\r\n                value,\r\n            });\r\n        } catch (error) {\r\n            // Error is handled by the useEffect hook for writeError\r\n            console.error(`Error submitting ${functionName}:`, error);\r\n            return {\r\n                error: error.message\r\n            };\r\n        }\r\n    }, [isConnected, writeContractAsync]);\r\n\r\n    /**\r\n     * Creates a new charity campaign.\r\n     */\r\n    const createCharity = useCallback(async (params) => {\r\n        const { owner, title, description, target, deadline, image, userId } = params;\r\n        // const targetInWei = parseEther(target.toString());\r\n        const result = await executeWrite(\r\n            'createCharity',\r\n            // [owner, title, description, targetInWei || 0, deadline || 0, image],\r\n            [owner, title, description, target, 0, image, userId],\r\n            undefined,\r\n            'Creating your charity...'\r\n        );\r\n\r\n        return result;\r\n    }, [executeWrite]);\r\n\r\n    /**\r\n     * Donates a specified amount to a charity.\r\n     */\r\n    const donateToCharity = useCallback(async (charityId, amount, userId) => {\r\n        const amountInWei = parseEther(amount.toString());\r\n        const result = await executeWrite(\r\n            'donateToCharity',\r\n            [charityId, userId],\r\n            amountInWei,\r\n            'Processing your donation...'\r\n        );\r\n        return result\r\n    }, [executeWrite]);\r\n\r\n    /**\r\n     * Sets the status of a charity (callable only by the charity owner).\r\n     * @param {number} charityId The ID of the charity.\r\n     * @param {number} newStatus The new status (0 for ACTIVE, 1 for INACTIVE).\r\n     */\r\n    const setCharityStatus = useCallback(async (charityId, newStatus) => {\r\n        await executeWrite(\r\n            'setCharityStatus',\r\n            [charityId, newStatus],\r\n            undefined,\r\n            'Updating charity status...'\r\n        );\r\n\r\n        return result\r\n    }, [executeWrite]);\r\n\r\n    return {\r\n        // Data\r\n        numberOfCharities,\r\n\r\n        // Actions\r\n        createCharity,\r\n        donateToCharity,\r\n        setCharityStatus, // <-- New function\r\n\r\n        // Refetch functions\r\n        refetchCharityCount,\r\n\r\n        // State\r\n        loading: isLoading || isWritePending || isConfirming,\r\n        isConnected,\r\n        address,\r\n        txHash,\r\n\r\n        // Utilities\r\n        formatEther,\r\n    };\r\n};\r\n\r\n// Hook for fetching multiple charities\r\nexport const useCharities = (limit) => {\r\n    const [charities, setCharities] = useState([]);\r\n    const [isLoading, setIsLoading] = useState(false);\r\n\r\n    const { data: numberOfCharities } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'numberOfCharities',\r\n    });\r\n\r\n    const totalCharities = numberOfCharities ? Number(numberOfCharities) : 0;\r\n    const charityLimit = limit ? Math.min(limit, totalCharities) : totalCharities;\r\n\r\n    // Fetch all charities\r\n    useEffect(() => {\r\n        if (totalCharities > 0) {\r\n            setIsLoading(true);\r\n            const fetchCharities = async () => {\r\n                const charityPromises = [];\r\n\r\n                for (let i = 0; i < charityLimit; i++) {\r\n                    charityPromises.push(\r\n                        fetch(`/api/v1/charity/${i}`).then(res => res.json()) // You'll need to implement this API endpoint\r\n                    );\r\n                }\r\n\r\n                try {\r\n                    const results = await Promise.all(charityPromises);\r\n                    setCharities(results.map((charity, index) => ({ ...charity, id: index })));\r\n                } catch (error) {\r\n                    console.error('Error fetching charities:', error);\r\n                } finally {\r\n                    setIsLoading(false);\r\n                }\r\n            };\r\n\r\n            fetchCharities();\r\n        }\r\n    }, [totalCharities, charityLimit]);\r\n\r\n    return {\r\n        charities,\r\n        isLoading,\r\n        totalCharities,\r\n    };\r\n};\r\n\r\n\r\n// --- Hook for a Single Charity's Data ---\r\nexport const useCharity = (charityId) => {\r\n    const { address } = useAccount();\r\n\r\n    const { data: charity, isLoading: isLoadingCharity, refetch: refetchCharity } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'getCharity',\r\n        args: [charityId],\r\n    });\r\n\r\n    const { data: donorContribution, isLoading: isLoadingContribution, refetch: refetchContribution } = useReadContract({\r\n        address: CONTRACT_ADDRESS,\r\n        abi: CHARIO_ABI,\r\n        functionName: 'getDonorContribution',\r\n        args: [charityId, address],\r\n        query: { enabled: !!address },\r\n    });\r\n\r\n    // Refetch all charity data\r\n    const refetchAll = useCallback(() => {\r\n        refetchCharity();\r\n        refetchContribution();\r\n    }, [refetchCharity, refetchContribution]);\r\n\r\n    return {\r\n        charity,\r\n        donorContribution,\r\n        isLoading: isLoadingCharity || isLoadingContribution,\r\n        isOwner: charity?.owner === address,\r\n        refetchAll,\r\n    };\r\n};\r\n\r\n// --- Utility Hook for Displaying Charity Status ---\r\nexport const useCharityStatus = (status) => {\r\n    const statusMap = {\r\n        0: 'Active',\r\n        1: 'Inactive',\r\n    };\r\n\r\n    const statusColors = {\r\n        0: 'green',\r\n        1: 'gray',\r\n    };\r\n\r\n    return {\r\n        statusText: statusMap[status] ?? 'Unknown',\r\n        statusColor: statusColors[status] ?? 'gray',\r\n        isActive: status === 0,\r\n        isInactive: status === 1,\r\n    };\r\n};"], "names": [], "mappings": "AAAA,gBAAgB;AAEhB,4DAA4D;AAC5D,WAAW;AACX,kBAAkB;AAClB,uBAAuB;AACvB,wBAAwB;AACxB,oCAAoC;AACpC,kBAAkB;AAClB,wEAAwE;AACxE,kDAAkD;AAClD,kCAAkC;AAElC,yBAAyB;AACzB,oEAAoE;AAEpE,mCAAmC;AACnC,qDAAqD;AACrD,yDAAyD;AACzD,gEAAgE;AAEhE,6BAA6B;AAC7B,gHAAgH;AAEhH,sCAAsC;AACtC,iGAAiG;AACjG,wBAAwB;AACxB,UAAU;AAEV,uCAAuC;AACvC,wBAAwB;AACxB,wBAAwB;AACxB,wDAAwD;AACxD,YAAY;AACZ,8CAA8C;AAC9C,uDAAuD;AACvD,mCAAmC;AACnC,sCAAsC;AACtC,YAAY;AACZ,kFAAkF;AAElF,wBAAwB;AACxB,4BAA4B;AAC5B,+DAA+D;AAC/D,wEAAwE;AACxE,mCAAmC;AACnC,sCAAsC;AACtC,YAAY;AACZ,wBAAwB;AAExB,4BAA4B;AAC5B,0FAA0F;AAC1F,qCAAqC;AACrC,2BAA2B;AAC3B,6CAA6C;AAC7C,UAAU;AAEV,6EAA6E;AAC7E,qCAAqC;AACrC,2BAA2B;AAC3B,2CAA2C;AAC3C,2BAA2B;AAC3B,mBAAmB;AACnB,kCAAkC;AAClC,aAAa;AACb,UAAU;AAEV,oDAAoD;AACpD,8FAA8F;AAC9F,8BAA8B;AAC9B,yDAAyD;AACzD,sBAAsB;AACtB,YAAY;AAEZ,8BAA8B;AAE9B,gBAAgB;AAChB,iDAAiD;AACjD,6CAA6C;AAC7C,mCAAmC;AACnC,8CAA8C;AAC9C,8BAA8B;AAC9B,gCAAgC;AAChC,kBAAkB;AAElB,sCAAsC;AACtC,wEAAwE;AACxE,4BAA4B;AAC5B,wEAAwE;AACxE,kEAAkE;AAClE,mCAAmC;AACnC,YAAY;AACZ,wCAAwC;AAExC,wBAAwB;AACxB,4DAA4D;AAC5D,iFAAiF;AAEjF,gBAAgB;AAChB,iEAAiE;AACjE,kCAAkC;AAClC,mCAAmC;AACnC,6EAA6E;AAC7E,6BAA6B;AAC7B,wCAAwC;AACxC,iBAAiB;AACjB,4BAA4B;AAC5B,+DAA+D;AAC/D,uDAAuD;AACvD,YAAY;AACZ,0BAA0B;AAE1B,2BAA2B;AAC3B,yEAAyE;AACzE,gBAAgB;AAChB,iEAAiE;AACjE,kCAAkC;AAClC,qCAAqC;AACrC,+BAA+B;AAC/B,+BAA+B;AAC/B,2CAA2C;AAC3C,iBAAiB;AACjB,4BAA4B;AAC5B,uDAAuD;AACvD,+CAA+C;AAC/C,YAAY;AACZ,0BAA0B;AAE1B,wBAAwB;AACxB,+DAA+D;AAC/D,8BAA8B;AAC9B,+BAA+B;AAC/B,2BAA2B;AAC3B,yBAAyB;AACzB,qCAAqC;AACrC,aAAa;AACb,0BAA0B;AAE1B,yBAAyB;AACzB,gEAAgE;AAChE,8BAA8B;AAC9B,gCAAgC;AAChC,2BAA2B;AAC3B,yBAAyB;AACzB,qCAAqC;AACrC,aAAa;AACb,0BAA0B;AAE1B,wBAAwB;AACxB,+DAA+D;AAC/D,8BAA8B;AAC9B,+BAA+B;AAC/B,2BAA2B;AAC3B,yBAAyB;AACzB,sCAAsC;AACtC,aAAa;AACb,0BAA0B;AAE1B,+BAA+B;AAC/B,qEAAqE;AACrE,8BAA8B;AAC9B,qCAAqC;AACrC,2BAA2B;AAC3B,yBAAyB;AACzB,mCAAmC;AACnC,aAAa;AACb,0BAA0B;AAE1B,mEAAmE;AAEnE,eAAe;AACf,kBAAkB;AAClB,6BAA6B;AAC7B,oBAAoB;AAEpB,qBAAqB;AACrB,yBAAyB;AACzB,2BAA2B;AAC3B,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AACzB,+BAA+B;AAE/B,+BAA+B;AAC/B,+BAA+B;AAC/B,2BAA2B;AAE3B,mBAAmB;AACnB,mBAAmB;AACnB,uBAAuB;AACvB,mBAAmB;AACnB,kBAAkB;AAElB,uBAAuB;AACvB,uBAAuB;AACvB,sBAAsB;AACtB,SAAS;AACT,KAAK;AAEL,0CAA0C;AAC1C,2CAA2C;AAC3C,sDAAsD;AACtD,yDAAyD;AAEzD,4DAA4D;AAC5D,qCAAqC;AACrC,2BAA2B;AAC3B,6CAA6C;AAC7C,UAAU;AAEV,gFAAgF;AAChF,qFAAqF;AAErF,6BAA6B;AAC7B,wBAAwB;AACxB,oCAAoC;AACpC,kCAAkC;AAClC,mDAAmD;AACnD,8CAA8C;AAE9C,2DAA2D;AAC3D,4CAA4C;AAC5C,8HAA8H;AAC9H,yBAAyB;AACzB,oBAAoB;AAEpB,wBAAwB;AACxB,0EAA0E;AAC1E,kGAAkG;AAClG,oCAAoC;AACpC,yEAAyE;AACzE,8BAA8B;AAC9B,2CAA2C;AAC3C,oBAAoB;AACpB,iBAAiB;AAEjB,gCAAgC;AAChC,YAAY;AACZ,0CAA0C;AAE1C,eAAe;AACf,qBAAqB;AACrB,qBAAqB;AACrB,0BAA0B;AAC1B,SAAS;AACT,KAAK;AAEL,+BAA+B;AAC/B,6CAA6C;AAC7C,wCAAwC;AAExC,wGAAwG;AACxG,qCAAqC;AACrC,2BAA2B;AAC3B,sCAAsC;AACtC,6BAA6B;AAC7B,UAAU;AAEV,4HAA4H;AAC5H,qCAAqC;AACrC,2BAA2B;AAC3B,gDAAgD;AAChD,sCAAsC;AACtC,mBAAmB;AACnB,kCAAkC;AAClC,aAAa;AACb,UAAU;AAEV,4GAA4G;AAC5G,qCAAqC;AACrC,2BAA2B;AAC3B,4CAA4C;AAC5C,6BAA6B;AAC7B,UAAU;AAEV,sFAAsF;AACtF,oDAAoD;AACpD,qEAAqE;AAErE,kCAAkC;AAClC,6CAA6C;AAC7C,4BAA4B;AAC5B,iCAAiC;AACjC,2BAA2B;AAC3B,gEAAgE;AAEhE,eAAe;AACf,mBAAmB;AACnB,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,mBAAmB;AACnB,qBAAqB;AACrB,sBAAsB;AACtB,SAAS;AACT,KAAK;AAEL,qCAAqC;AACrC,gDAAgD;AAChD,0BAA0B;AAC1B,uBAAuB;AACvB,yBAAyB;AACzB,0BAA0B;AAC1B,yBAAyB;AACzB,SAAS;AAET,6BAA6B;AAC7B,sBAAsB;AACtB,uBAAuB;AACvB,qBAAqB;AACrB,mBAAmB;AACnB,SAAS;AAET,eAAe;AACf,sDAAsD;AACtD,uDAAuD;AACvD,kCAAkC;AAClC,qCAAqC;AACrC,qCAAqC;AACrC,oCAAoC;AACpC,SAAS;AACT,KAAK;AAEL,uCAAuC;AACvC,iDAAiD;AACjD,sDAAsD;AACtD,8BAA8B;AAC9B,2CAA2C;AAE3C,uCAAuC;AACvC,oEAAoE;AAEpE,eAAe;AACf,wBAAwB;AACxB,qBAAqB;AACrB,8CAA8C;AAC9C,gEAAgE;AAChE,yEAAyE;AACzE,SAAS;AACT,KAAK;;;;;;;AAkBoB;AAbzB;AACA;AAAA;AAAA;AAAA;AAMA,oKAAqC,2DAA2D;AAChG;AAAA;AACA;;AAXA;;;;;;AAaA,wBAAwB;AACxB,MAAM,aAAa,4FAAA,CAAA,UAAG;AACtB,MAAM;AAGC,MAAM,YAAY;;IACrB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,MAAM,EAAE,MAAM,MAAM,EAAE,kBAAkB,EAAE,OAAO,UAAU,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD;IAE1G,kDAAkD;IAClD,MAAM,EAAE,WAAW,YAAY,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,gLAAA,CAAA,+BAA4B,AAAD,EAAE;QAAE,MAAM;IAAO;IAExG,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,aAAa;gBACb,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,aAAa;YACjB;QACJ;8BAAG;QAAC;KAAY;IAEhB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACN,IAAI,YAAY;gBACZ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,YAAY,IAAI;gBACvC,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,aAAa;YACjB;QACJ;8BAAG;QAAC;KAAW;IAEf,qBAAqB;IACrB,MAAM,EAAE,MAAM,iBAAiB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE;QAC9E,SAAS;QACT,KAAK;QACL,cAAc;IAClB;IAEA,6CAA6C;IAC7C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,OAAO,cAAc,MAAM,OAAO;YAC/D,IAAI,CAAC,aAAa;gBACd,OAAO;oBACH,OAAO;oBACP,aAAa;gBACjB;YACJ;YACA,aAAa;YACb,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,kBAAkB;YAE7B,IAAI;gBACA,MAAM,mBAAmB;oBACrB,SAAS;oBACT,KAAK;oBACL;oBACA;oBACA;gBACJ;YACJ,EAAE,OAAO,OAAO;gBACZ,wDAAwD;gBACxD,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC,EAAE;gBACnD,OAAO;oBACH,OAAO,MAAM,OAAO;gBACxB;YACJ;QACJ;8CAAG;QAAC;QAAa;KAAmB;IAEpC;;KAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO;YACrC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;YACvE,qDAAqD;YACrD,MAAM,UAAS,MAAM,aACjB,iBACA,uEAAuE;YACvE;gBAAC;gBAAO;gBAAO;gBAAa;gBAAQ;gBAAG;gBAAO;aAAO,EACrD,WACA;YAGJ,OAAO;QACX;+CAAG;QAAC;KAAa;IAEjB;;KAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAAO,WAAW,QAAQ;YAC1D,MAAM,cAAc,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO,QAAQ;YAC9C,MAAM,UAAS,MAAM,aACjB,mBACA;gBAAC;gBAAW;aAAO,EACnB,aACA;YAEJ,OAAO;QACX;iDAAG;QAAC;KAAa;IAEjB;;;;KAIC,GACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO,WAAW;YACnD,MAAM,aACF,oBACA;gBAAC;gBAAW;aAAU,EACtB,WACA;YAGJ,OAAO;QACX;kDAAG;QAAC;KAAa;IAEjB,OAAO;QACH,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;QAEA,oBAAoB;QACpB;QAEA,QAAQ;QACR,SAAS,aAAa,kBAAkB;QACxC;QACA;QACA;QAEA,YAAY;QACZ,aAAA,+JAAA,CAAA,cAAW;IACf;AACJ;GAlIa;;QACwB,8JAAA,CAAA,aAAU;QAIgD,oKAAA,CAAA,mBAAgB;QAG/C,gLAAA,CAAA,+BAA4B;QAoBtB,mKAAA,CAAA,kBAAe;;;AAyG9E,MAAM,eAAe,CAAC;;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE;QAChD,SAAS;QACT,KAAK;QACL,cAAc;IAClB;IAEA,MAAM,iBAAiB,oBAAoB,OAAO,qBAAqB;IACvE,MAAM,eAAe,QAAQ,KAAK,GAAG,CAAC,OAAO,kBAAkB;IAE/D,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACN,IAAI,iBAAiB,GAAG;gBACpB,aAAa;gBACb,MAAM;6DAAiB;wBACnB,MAAM,kBAAkB,EAAE;wBAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;4BACnC,gBAAgB,IAAI,CAChB,MAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE,IAAI;yEAAC,CAAA,MAAO,IAAI,IAAI;wEAAI,6CAA6C;;wBAE3G;wBAEA,IAAI;4BACA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;4BAClC,aAAa,QAAQ,GAAG;yEAAC,CAAC,SAAS,QAAU,CAAC;wCAAE,GAAG,OAAO;wCAAE,IAAI;oCAAM,CAAC;;wBAC3E,EAAE,OAAO,OAAO;4BACZ,QAAQ,KAAK,CAAC,6BAA6B;wBAC/C,SAAU;4BACN,aAAa;wBACjB;oBACJ;;gBAEA;YACJ;QACJ;iCAAG;QAAC;QAAgB;KAAa;IAEjC,OAAO;QACH;QACA;QACA;IACJ;AACJ;IA7Ca;;QAI2B,mKAAA,CAAA,kBAAe;;;AA6ChD,MAAM,aAAa,CAAC;;IACvB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,EAAE,MAAM,OAAO,EAAE,WAAW,gBAAgB,EAAE,SAAS,cAAc,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE;QAC5F,SAAS;QACT,KAAK;QACL,cAAc;QACd,MAAM;YAAC;SAAU;IACrB;IAEA,MAAM,EAAE,MAAM,iBAAiB,EAAE,WAAW,qBAAqB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE;QAChH,SAAS;QACT,KAAK;QACL,cAAc;QACd,MAAM;YAAC;YAAW;SAAQ;QAC1B,OAAO;YAAE,SAAS,CAAC,CAAC;QAAQ;IAChC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC3B;YACA;QACJ;6CAAG;QAAC;QAAgB;KAAoB;IAExC,OAAO;QACH;QACA;QACA,WAAW,oBAAoB;QAC/B,SAAS,SAAS,UAAU;QAC5B;IACJ;AACJ;IA/Ba;;QACW,8JAAA,CAAA,aAAU;QAEkD,mKAAA,CAAA,kBAAe;QAOK,mKAAA,CAAA,kBAAe;;;AAwBhH,MAAM,mBAAmB,CAAC;IAC7B,MAAM,YAAY;QACd,GAAG;QACH,GAAG;IACP;IAEA,MAAM,eAAe;QACjB,GAAG;QACH,GAAG;IACP;IAEA,OAAO;QACH,YAAY,SAAS,CAAC,OAAO,IAAI;QACjC,aAAa,YAAY,CAAC,OAAO,IAAI;QACrC,UAAU,WAAW;QACrB,YAAY,WAAW;IAC3B;AACJ", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/hooks/use-sse.js"], "sourcesContent": ["export function createReconnectingEventSource(url, options = {}) {\r\n    let eventSource = null\r\n    let reconnectTimeout = null\r\n    let reconnectDelay = 1000\r\n    const maxReconnectDelay = 30000\r\n    const { onOpen, onMessage, onError, onEvent, onStatusChange } = options\r\n\r\n    function setStatus(status) {\r\n        if (onStatusChange) onStatusChange(status)\r\n    }\r\n\r\n    function connect() {\r\n        setStatus('connecting')\r\n        eventSource = new EventSource(url)\r\n\r\n        eventSource.onopen = (event) => {\r\n            reconnectDelay = 1000\r\n            setStatus(1)\r\n            if (onOpen) onOpen(event)\r\n            console.log('SSE connected')\r\n        }\r\n\r\n        eventSource.onmessage = (event) => {\r\n            if (onMessage) onMessage(event)\r\n        }\r\n\r\n        eventSource.onerror = (event) => {\r\n            if (onError) onError(event)\r\n            setStatus(0)\r\n            console.error('SSE error, attempting to reconnect...')\r\n            eventSource.close()\r\n\r\n            if (reconnectTimeout) clearTimeout(reconnectTimeout)\r\n\r\n            reconnectTimeout = setTimeout(() => {\r\n                reconnectDelay = Math.min(reconnectDelay * 2, maxReconnectDelay)\r\n                connect()\r\n            }, reconnectDelay)\r\n        }\r\n\r\n        if (onEvent && typeof onEvent === 'object') {\r\n            Object.entries(onEvent).forEach(([eventName, handler]) => {\r\n                eventSource.addEventListener(eventName, handler)\r\n            })\r\n        }\r\n    }\r\n\r\n    connect()\r\n\r\n    return {\r\n        close: () => {\r\n            if (reconnectTimeout) clearTimeout(reconnectTimeout)\r\n            if (eventSource) eventSource.close()\r\n            setStatus('disconnected')\r\n            console.log('SSE connection closed manually')\r\n        },\r\n        getEventSource: () => eventSource,\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,8BAA8B,GAAG,EAAE,UAAU,CAAC,CAAC;IAC3D,IAAI,cAAc;IAClB,IAAI,mBAAmB;IACvB,IAAI,iBAAiB;IACrB,MAAM,oBAAoB;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG;IAEhE,SAAS,UAAU,MAAM;QACrB,IAAI,gBAAgB,eAAe;IACvC;IAEA,SAAS;QACL,UAAU;QACV,cAAc,IAAI,YAAY;QAE9B,YAAY,MAAM,GAAG,CAAC;YAClB,iBAAiB;YACjB,UAAU;YACV,IAAI,QAAQ,OAAO;YACnB,QAAQ,GAAG,CAAC;QAChB;QAEA,YAAY,SAAS,GAAG,CAAC;YACrB,IAAI,WAAW,UAAU;QAC7B;QAEA,YAAY,OAAO,GAAG,CAAC;YACnB,IAAI,SAAS,QAAQ;YACrB,UAAU;YACV,QAAQ,KAAK,CAAC;YACd,YAAY,KAAK;YAEjB,IAAI,kBAAkB,aAAa;YAEnC,mBAAmB,WAAW;gBAC1B,iBAAiB,KAAK,GAAG,CAAC,iBAAiB,GAAG;gBAC9C;YACJ,GAAG;QACP;QAEA,IAAI,WAAW,OAAO,YAAY,UAAU;YACxC,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,WAAW,QAAQ;gBACjD,YAAY,gBAAgB,CAAC,WAAW;YAC5C;QACJ;IACJ;IAEA;IAEA,OAAO;QACH,OAAO;YACH,IAAI,kBAAkB,aAAa;YACnC,IAAI,aAAa,YAAY,KAAK;YAClC,UAAU;YACV,QAAQ,GAAG,CAAC;QAChB;QACA,gBAAgB,IAAM;IAC1B;AACJ", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/ui/progress.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}) {\r\n  return (\r\n    (<ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn(\r\n        \"bg-primary/20 relative h-1 max-h-1 w-full overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-emerald-500 dark:bg-emerald-600 h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }} />\r\n    </ProgressPrimitive.Root>)\r\n  );\r\n}\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACJ;IACC,qBACG,6LAAC,uKAAA,CAAA,OAAsB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;kBACT,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAGlE;KAnBS", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/app/home/<USER>/%5BcharityId%5D/_components/charity-page.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { useChario } from '@/hooks/use-chario';\r\nimport { createReconnectingEventSource } from '@/hooks/use-sse';\r\nimport { authClient, useSession } from '@/lib/auth-client';\r\nimport { addEthAmounts, cn, sleep } from '@/lib/utils';\r\nimport { CheckIcon, HandCoinsIcon, Loader2Icon, QrCodeIcon, UserIcon } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport React, { useEffect, useState } from 'react'\r\nimport { toast } from 'sonner';\r\nimport { format } from 'date-fns'\r\nimport Link from 'next/link';\r\nimport { FaQuestion } from 'react-icons/fa';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { useModal } from '@/hooks/use-modal-store';\r\n\r\nfunction CharityPage({ charity: rawCharity }) {\r\n  const { data: userData } = useSession();\r\n  const [charity, setCharity] = useState();\r\n  const { donateToCharity } = useChario();\r\n  const [realtime, setRealtime] = useState(false);\r\n  const [donations, setDonations] = useState([]);\r\n  const [amountToDonate, setAmountToDonate] = useState();\r\n  const [isDonating, setIsDonating] = useState(false);\r\n  const [totalCollected, setTotalCollected] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const newCharity = JSON.parse(rawCharity)\r\n\r\n    setCharity(newCharity)\r\n    setDonations(newCharity.donations)\r\n    setTotalCollected(newCharity.amountCollected)\r\n\r\n    const sse = createReconnectingEventSource(`${process.env.NEXT_PUBLIC_SSE_URL}/sse/charities/${newCharity.id}/donations`, {\r\n\r\n      onOpen: () => console.log('Connected to SSE'),\r\n\r\n      onMessage: (e) => console.log('Message:', e.data),\r\n\r\n      onError: (e) => console.error('Error:', e),\r\n\r\n      onEvent: {\r\n        'new-donation': (e) => {\r\n          const data = JSON.parse(e.data)\r\n          console.log('New charity event:', data.donation);\r\n\r\n          setDonations((prevDonations) => [data.donation, ...prevDonations])\r\n          setTotalCollected(prev => {\r\n            const amountFloat = data.donation.amountEth || 0;\r\n            const total = addEthAmounts(prev, amountFloat);\r\n            return total;\r\n          });\r\n        }\r\n      },\r\n      onStatusChange: (status) => setRealtime(status === 1 ? true : false)\r\n    })\r\n\r\n    return () => {\r\n      sse.close()\r\n    }\r\n\r\n  }, [rawCharity])\r\n\r\n  async function onDonate() {\r\n    setIsDonating(true);\r\n    try {\r\n      if (!amountToDonate || isNaN(amountToDonate) || amountToDonate <= 0) {\r\n        toast.error('Invalid amount');\r\n        return;\r\n      }\r\n      let userId = userData?.user?.id;\r\n      if (userId === null || userId === \"\" || userId === undefined) {\r\n        const data = await authClient.signIn.anonymous()\r\n        userId = data.data.user.id;\r\n      }\r\n      const result = await donateToCharity(charity.id, amountToDonate, userId);\r\n      await sleep(1000)\r\n      console.log(\"Donation result:\", result)\r\n      if (result?.error && result?.walletError) {\r\n        toast.error(result.error);\r\n      }\r\n    } catch (error) {\r\n      console.log(error)\r\n      setIsDonating(false);\r\n      toast.error(\"Something went wrong\");\r\n    } finally {\r\n      setIsDonating(false);\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className='p-2 md:p-6 lg:p-8 pt-10'>\r\n      <div className='grid grid-cols-1 lg:grid-cols-2 gap-8'>\r\n        <div className='flex flex-col'>\r\n          <div className=\"relative w-full h-80 rounded-xl overflow-hidden ring ring-border mb-4\">\r\n            {charity?.image && (<Image\r\n              src={charity?.image}\r\n              alt={charity?.title}\r\n              fill\r\n              className=\"object-cover\"\r\n              style={{ objectFit: 'cover' }}\r\n            />)}\r\n          </div>\r\n          <div className='flex justify-between items-center'>\r\n            <h2 className='text-2xl font-bold mb-5 line-clamp-2'>\r\n              {charity?.title}\r\n            </h2>\r\n            <div className='flex gap-2 items-center -mt-2 text-lg font-semibold'>\r\n              {charity?.owner?.name}\r\n              {charity?.owner && (<Avatar\r\n                isAnonymousUser={false}\r\n                user={charity?.owner}\r\n              />)}\r\n            </div>\r\n          </div>\r\n          <div className='flex flex-row gap-4 mb-3'>\r\n            <input\r\n              className={cn(\r\n                'outline-none ring ring-input rounded-md px-2',\r\n                isDonating && 'opacity-75'\r\n              )}\r\n              type=\"number\"\r\n              placeholder='0.00'\r\n              step={0.001}\r\n              value={amountToDonate}\r\n              onChange={(e) => setAmountToDonate(e.target.value)}\r\n              disabled={isDonating}\r\n            />\r\n            <Button\r\n              className='w-max cursor-pointer '\r\n              disabled={isDonating}\r\n              onClick={onDonate}\r\n            >\r\n              {isDonating ? (\r\n                <Loader2Icon className='animate-spin' />\r\n              ) : (\r\n                'Donate'\r\n              )}\r\n            </Button>\r\n            {/* <Button\r\n            >\r\n              <QrCodeIcon />\r\n            </Button> */}\r\n          </div>\r\n          {/* <p>\r\n            {charity?.ownerWallet}\r\n          </p> */}\r\n          <p className='text-muted-foreground text-sm mb-8'>\r\n            {charity?.description}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <Donations\r\n            donations={donations}\r\n            realtime={realtime}\r\n            totalCollected={totalCollected}\r\n          />\r\n          {(charity?.target !== '0' && charity?.target) && (\r\n            <div className=\"flex flex-col gap-1 mt-6\">\r\n              <Progress\r\n                value={(totalCollected / charity.target) * 100}\r\n                className='h-2 bg-zinc-700'\r\n              />\r\n              <div className='flex justify-between text-xs'>\r\n                <span className='text-zinc-400'>Target</span>\r\n                <span className='text-primary font-sm'>\r\n                  {charity.target} ETH\r\n                </span>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <p className='text-muted-foreground text-sm mb-8 mt-4'>\r\n            <span className='text-accent-foreground font-semibold'>Owners wallet:</span> {charity?.ownerWallet}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default CharityPage\r\n\r\nfunction Donations({ donations, realtime, totalCollected }) {\r\n  return (\r\n    <div className='flex flex-col gap-1 bg-card rounded-3xl p-4 py-3 ring-1 ring-card-foreground/10 border-4 border-card/40 h-max'>\r\n      <div className='flex flex-row justify-between items-center '>\r\n        <div className='flex items-center'>\r\n          <h2 className='text-xl font-semibold mb-4'>\r\n            Donations - <span className='text-emerald-400'>{totalCollected} ETH</span>\r\n          </h2>\r\n        </div>\r\n        <RealtimeIndicator\r\n          realtime={realtime}\r\n          className='-top-4 right-3'\r\n        />\r\n      </div>\r\n      <div className='ring ring-input rounded-md max-h-[400px] overflow-y-auto'>\r\n        {donations?.length > 0 ? (\r\n          <div className='flex flex-col '>\r\n            {donations.map((donation, index) => (\r\n              <Donation key={donation.id} donation={donation} isLastItem={donations.length - 1 === index} />\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className='flex flex-col justify-center items-center py-4'>\r\n            <HandCoinsIcon size={18} className='text-primary mb-1' />\r\n            <p className='text-muted-foreground text-sm'>\r\n              No donations yet\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction Donation({ donation, isLastItem }) {\r\n  // console.log(\"Donation:\", donation)\r\n  const { onOpen } = useModal()\r\n  return (\r\n    <div\r\n      className={cn('flex items-center justify-between gap-2 py-3 pr-4 px-2 hover:bg-accent cursor-pointer', !isLastItem && 'border-b border-accent')}\r\n      onClick={() => onOpen('donationAction', { donation })}\r\n    >\r\n      <div className='flex gap-2 items-center'>\r\n        <Avatar isAnonymousUser={donation?.donorUser?.isAnonymous} user={donation?.donorUser} />\r\n        <div >\r\n          <Link\r\n            href={`/home/<USER>/${donation.charityId}`}\r\n          >\r\n            <p className='text-md'>\r\n              {`${donation.txHash.substring(0, 8)}...${donation.txHash.substring(donation.txHash.length - 6, donation.txHash.length)}`}\r\n              {/* {donation.senderWallet} */}\r\n            </p>\r\n          </Link>\r\n          <p className='text-xs text-muted-foreground'>\r\n            {format(donation.createdAt, 'dd.MM.yy HH:mm')}\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <p className='text-emerald-500 dark:text-emerald-300'>\r\n        {donation.amountEth} ETH\r\n      </p>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction Avatar({ user, isAnonymousUser }) {\r\n  // console.log(\"SenderAvatar:\", user)\r\n  return (\r\n    <div className={cn(\r\n      'min-w-8 min-h-8 w-8 h-8 max-w-8 max-h-8 lg:w-10 lg:h-10 lg:max-w-10 lg:max-h-10 rounded-full bg-accent flex items-center justify-center relative',\r\n      !isAnonymousUser && user.verifiedLevel !== 0 && 'p-[4px] ring-1',\r\n      {\r\n        ' ring-yellow-300': !isAnonymousUser && user.verifiedLevel === 1,\r\n        ' ring-green-300': !isAnonymousUser && user.verifiedLevel === 2,\r\n        ' ring-blue-300': !isAnonymousUser && user.verifiedLevel === 3,\r\n        ' ring-gray-300': !isAnonymousUser && user.verifiedLevel === 4,\r\n      }\r\n    )}>\r\n      {isAnonymousUser ? (\r\n        <div className='relative w-full h-full rounded-full bg-card flex items-center justify-center'>\r\n          <UserIcon size={18} className='text-primary' />\r\n          <FaQuestion className='absolute top-0.5 right-0.5 text-primary text-[8px]' />\r\n        </div>\r\n      ) : (\r\n        <div className='relative w-full h-full rounded-full overflow-hidden bg-card flex items-center justify-center'>\r\n          <img src={user?.image} alt={'user'} className='w-full h-full object-cover' />\r\n        </div>\r\n      )}\r\n      {user.verifiedLevel === 4 && (\r\n        <div className='absolute -top-1 -right-1 p-1 bg-gray-700 rounded-full'>\r\n          <CheckIcon size={12} className='text-white' />\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction RealtimeIndicator({ realtime, className }) {\r\n  return (\r\n    <div className={cn('relative', className)}>\r\n      <div\r\n        className={cn(\r\n          'absolute bg-black bg-opacity-50 z-10 h-2 w-2 rounded-full',\r\n          realtime ? 'bg-emerald-500' : 'bg-yellow-500'\r\n        )}\r\n      />\r\n      <div\r\n        className={cn(\r\n          // Base size for the animation (the max size it will reach)\r\n          'absolute bg-black bg-opacity-50 z-0 h-6 w-6 rounded-full',\r\n          // Apply the animation class\r\n          'animate-pulse-fade-out',\r\n          // Adjust initial positioning if needed due to h-4/w-4 base\r\n          '-top-2 -left-2', // This positioning might need slight adjustment based on your h-4/w-4\r\n          realtime ? 'bg-emerald-500' : 'bg-yellow-500'\r\n        )}\r\n      />\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;AAmCiD;;AAjCjD;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;;;;;;;AAkBA,SAAS,YAAY,EAAE,SAAS,UAAU,EAAE;;IAC1C,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACrC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,YAAS,AAAD;IACpC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,aAAa,KAAK,KAAK,CAAC;YAE9B,WAAW;YACX,aAAa,WAAW,SAAS;YACjC,kBAAkB,WAAW,eAAe;YAE5C,MAAM,MAAM,CAAA,GAAA,6HAAA,CAAA,gCAA6B,AAAD,EAAE,2EAAmC,eAAe,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,EAAE;gBAEvH,MAAM;iDAAE,IAAM,QAAQ,GAAG,CAAC;;gBAE1B,SAAS;iDAAE,CAAC,IAAM,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI;;gBAEhD,OAAO;iDAAE,CAAC,IAAM,QAAQ,KAAK,CAAC,UAAU;;gBAExC,SAAS;oBACP,cAAc;qDAAE,CAAC;4BACf,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE,IAAI;4BAC9B,QAAQ,GAAG,CAAC,sBAAsB,KAAK,QAAQ;4BAE/C;6DAAa,CAAC,gBAAkB;wCAAC,KAAK,QAAQ;2CAAK;qCAAc;;4BACjE;6DAAkB,CAAA;oCAChB,MAAM,cAAc,KAAK,QAAQ,CAAC,SAAS,IAAI;oCAC/C,MAAM,QAAQ,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;oCAClC,OAAO;gCACT;;wBACF;;gBACF;gBACA,cAAc;iDAAE,CAAC,SAAW,YAAY,WAAW,IAAI,OAAO;;YAChE;YAEA;yCAAO;oBACL,IAAI,KAAK;gBACX;;QAEF;gCAAG;QAAC;KAAW;IAEf,eAAe;QACb,cAAc;QACd,IAAI;YACF,IAAI,CAAC,kBAAkB,MAAM,mBAAmB,kBAAkB,GAAG;gBACnE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,IAAI,SAAS,UAAU,MAAM;YAC7B,IAAI,WAAW,QAAQ,WAAW,MAAM,WAAW,WAAW;gBAC5D,MAAM,OAAO,MAAM,+HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAS;gBAC9C,SAAS,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B;YACA,MAAM,SAAS,MAAM,gBAAgB,QAAQ,EAAE,EAAE,gBAAgB;YACjE,MAAM,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE;YACZ,QAAQ,GAAG,CAAC,oBAAoB;YAChC,IAAI,QAAQ,SAAS,QAAQ,aAAa;gBACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,cAAc;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,SAAS,uBAAU,6LAAC,gIAAA,CAAA,UAAK;gCACxB,KAAK,SAAS;gCACd,KAAK,SAAS;gCACd,IAAI;gCACJ,WAAU;gCACV,OAAO;oCAAE,WAAW;gCAAQ;;;;;;;;;;;sCAGhC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,SAAS;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;wCACZ,SAAS,OAAO;wCAChB,SAAS,uBAAU,6LAAC;4CACnB,iBAAiB;4CACjB,MAAM,SAAS;;;;;;;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gDACA,cAAc;oCAEhB,MAAK;oCACL,aAAY;oCACZ,MAAM;oCACN,OAAO;oCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACjD,UAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU;oCACV,SAAS;8CAER,2BACC,6LAAC,wNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;+CAEvB;;;;;;;;;;;;sCAWN,6LAAC;4BAAE,WAAU;sCACV,SAAS;;;;;;;;;;;;8BAGd,6LAAC;;sCACC,6LAAC;4BACC,WAAW;4BACX,UAAU;4BACV,gBAAgB;;;;;;wBAEhB,SAAS,WAAW,OAAO,SAAS,wBACpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uIAAA,CAAA,WAAQ;oCACP,OAAO,AAAC,iBAAiB,QAAQ,MAAM,GAAI;oCAC3C,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;4CAAK,WAAU;;gDACb,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAKxB,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;gCAAqB;gCAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;AAMnG;GAlKS;;QACoB,+HAAA,CAAA,aAAU;QAET,gIAAA,CAAA,YAAS;;;KAH9B;uCAoKM;AAEf,SAAS,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE;IACxD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAA6B;8CAC7B,6LAAC;oCAAK,WAAU;;wCAAoB;wCAAe;;;;;;;;;;;;;;;;;;kCAGnE,6LAAC;wBACC,UAAU;wBACV,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBAAI,WAAU;0BACZ,WAAW,SAAS,kBACnB,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;4BAA2B,UAAU;4BAAU,YAAY,UAAU,MAAM,GAAG,MAAM;2BAAtE,SAAS,EAAE;;;;;;;;;yCAI9B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,gBAAa;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;;AAQzD;MAhCS;AAkCT,SAAS,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE;;IACxC,qCAAqC;IACrC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yFAAyF,CAAC,cAAc;QACtH,SAAS,IAAM,OAAO,kBAAkB;gBAAE;YAAS;;0BAEnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,iBAAiB,UAAU,WAAW;wBAAa,MAAM,UAAU;;;;;;kCAC3E,6LAAC;;0CACC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,gBAAgB,EAAE,SAAS,SAAS,EAAE;0CAE7C,cAAA,6LAAC;oCAAE,WAAU;8CACV,GAAG,SAAS,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,GAAG,SAAS,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;0CAI5H,6LAAC;gCAAE,WAAU;0CACV,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS,EAAE;;;;;;;;;;;;;;;;;;0BAIlC,6LAAC;gBAAE,WAAU;;oBACV,SAAS,SAAS;oBAAC;;;;;;;;;;;;;AAI5B;IA7BS;;QAEY,wIAAA,CAAA,WAAQ;;;MAFpB;AA+BT,SAAS,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE;IACvC,qCAAqC;IACrC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oJACA,CAAC,mBAAmB,KAAK,aAAa,KAAK,KAAK,kBAChD;YACE,oBAAoB,CAAC,mBAAmB,KAAK,aAAa,KAAK;YAC/D,mBAAmB,CAAC,mBAAmB,KAAK,aAAa,KAAK;YAC9D,kBAAkB,CAAC,mBAAmB,KAAK,aAAa,KAAK;YAC7D,kBAAkB,CAAC,mBAAmB,KAAK,aAAa,KAAK;QAC/D;;YAEC,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC9B,6LAAC,iJAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;qCAGxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,KAAK,MAAM;oBAAO,KAAK;oBAAQ,WAAU;;;;;;;;;;;YAGjD,KAAK,aAAa,KAAK,mBACtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2MAAA,CAAA,YAAS;oBAAC,MAAM;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzC;MA9BS;AAgCT,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAAE;IAChD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7B,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,WAAW,mBAAmB;;;;;;0BAGlC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DAA2D;gBAC3D,4DACA,4BAA4B;gBAC5B,0BACA,2DAA2D;gBAC3D,kBACA,WAAW,mBAAmB;;;;;;;;;;;;AAKxC;MAtBS", "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/js/web3/packages/chario/src/components/md/not-found.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport Link from 'next/link'\r\nimport { CopyXIcon } from 'lucide-react'\r\n\r\nfunction NotFound({ title, description }) {\r\n    return (\r\n        <div className='h-full w-full flex flex-col items-center justify-center p-8 text-center'>\r\n            <CopyXIcon size={40} className='text-primary mb-6' />\r\n            <h1 className='font-bold text-2xl mb-1'>{title}</h1>\r\n            <p className='text-muted-foreground text-xs mb-8'>{description}</p>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default NotFound"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,SAAS,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE;IACpC,qBACI,6LAAC;QAAI,WAAU;;0BACX,6LAAC,+MAAA,CAAA,YAAS;gBAAC,MAAM;gBAAI,WAAU;;;;;;0BAC/B,6LAAC;gBAAG,WAAU;0BAA2B;;;;;;0BACzC,6LAAC;gBAAE,WAAU;0BAAsC;;;;;;;;;;;;AAG/D;KARS;uCAUM", "debugId": null}}]}