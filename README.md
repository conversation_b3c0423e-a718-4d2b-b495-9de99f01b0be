# 🎯 Chario - Decentralized Charity Platform

> **🚀 Live Demo:** [Coming Soon - Deploy your own instance!](#deployment)

A modern, decentralized charity platform built on Ethereum that enables transparent, secure, and efficient charitable donations using blockchain technology. Create charity campaigns, accept donations in ETH, and track all transactions on-chain with real-time updates.

## ✨ Features

- 🔗 **Web3 Integration**: Connect with MetaMask and other Web3 wallets via RainbowKit
- 💰 **ETH Donations**: Accept donations directly in Ethereum with automatic USD conversion
- 📊 **Real-time Analytics**: Live donation tracking with charts and statistics
- 🔐 **Secure Authentication**: Email/password and Google OAuth via Better Auth
- 📱 **Responsive Design**: Modern UI built with Next.js 15, Tailwind CSS, and Shadcn/ui
- 🗄️ **Database Integration**: PostgreSQL with Prisma ORM for data persistence
- 📁 **File Storage**: IPFS integration via Filebase for decentralized image storage
- ⚡ **Real-time Updates**: Live donation feeds using Server-Sent Events
- 🔍 **Blockchain Monitoring**: Automated smart contract event listening
- 📧 **Email Notifications**: Automated emails via Resend for verification and updates

## 🏗️ Architecture

This project consists of three main components:

### 1. **Frontend (chario/)** - Next.js 15 Application
- Modern React app with App Router
- Web3 integration with Wagmi and RainbowKit
- Real-time UI updates and responsive design

### 2. **Smart Contracts (contracts/)** - Hardhat Development Environment
- Solidity smart contracts for charity management
- Hardhat for testing, deployment, and local blockchain
- OpenZeppelin contracts for security

### 3. **Blockchain Listener (listener/)** - Event Monitoring Service
- Hono.js server for blockchain event monitoring
- Real-time donation tracking and database updates
- RESTful API for frontend integration

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm/yarn/pnpm
- **PostgreSQL** database
- **Git** for version control
- **MetaMask** or compatible Web3 wallet

### 1. Clone the Repository

```bash
git clone <your-repository-url>
cd chario-platform
```

### 2. Install Dependencies

```bash
# Install dependencies for all packages
cd chario && npm install
cd ../contracts && npm install  
cd ../listener && npm install
```

### 3. Environment Setup

Create the following environment files:

#### **chario/.env.local**
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/chario_db"

# Authentication
BETTER_AUTH_SECRET="your-super-secret-key-here"
BETTER_AUTH_URL="http://localhost:3000"

# Google OAuth (Optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email Service (Resend)
RESEND_API_KEY="your-resend-api-key"

# File Storage (Filebase/IPFS)
AWS_ACCESS_KEY_ID="your-filebase-access-key"
AWS_SECRET_ACCESS_KEY="your-filebase-secret-key"
S3_ENDPOINT="https://s3.filebase.com"
S3_BUCKET_NAME="your-bucket-name"
NEXT_PUBLIC_IPFS_GATEWAY="https://influential-violet-muskox.myfilebase.com/ipfs"

# Blockchain
NEXT_PUBLIC_CONTRACT_ADDRESS="0x..." # Set after contract deployment
NEXT_PUBLIC_RPC_URL="http://127.0.0.1:8545" # Local Hardhat node
NEXT_PUBLIC_RAINBOWKIT_PROJECT_ID="your-walletconnect-project-id"
```

#### **contracts/.env**
```env
# Alchemy API for Sepolia testnet (optional)
ALCHEMY_API_KEY="your-alchemy-api-key"

# Private key for deployment (testnet only!)
SEPOLIA_PRIVATE_KEY="your-sepolia-private-key"

# Etherscan API for contract verification
ETHERSCAN_API_KEY="your-etherscan-api-key"
```

#### **listener/.env**
```env
# Database (same as frontend)
DATABASE_URL="postgresql://username:password@localhost:5432/chario_db"

# Blockchain
CONTRACT_ADDRESS="0x..." # Set after contract deployment
RPC_URL="http://127.0.0.1:8545" # Local Hardhat node
```

### 4. Database Setup

```bash
# Navigate to chario directory
cd chario

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# (Optional) Seed the database
npx prisma db seed
```

### 5. Smart Contract Deployment

```bash
# Navigate to contracts directory
cd contracts

# Start local Hardhat node (keep this running)
npm run start

# In a new terminal, deploy contracts
npm run deploy

# Copy the deployed contract address to your .env files
```

### 6. Start the Development Servers

Open **three separate terminals**:

**Terminal 1 - Frontend:**
```bash
cd chario
npm run dev
```

**Terminal 2 - Blockchain Listener:**
```bash
cd listener
npm run dev
```

**Terminal 3 - Local Blockchain (if not already running):**
```bash
cd contracts
npm run start
```

### 7. Access the Application

- **Frontend**: http://localhost:3000
- **Blockchain Listener API**: http://localhost:3001
- **Local Blockchain RPC**: http://localhost:8545

## 📋 Environment Variables Reference

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://user:pass@localhost:5432/db` |
| `BETTER_AUTH_SECRET` | Secret key for authentication | `your-secret-key` |
| `BETTER_AUTH_URL` | Base URL for auth callbacks | `http://localhost:3000` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_CLIENT_ID` | Google OAuth client ID | - |
| `GOOGLE_CLIENT_SECRET` | Google OAuth client secret | - |
| `RESEND_API_KEY` | Resend email service API key | - |
| `AWS_ACCESS_KEY_ID` | Filebase access key | - |
| `AWS_SECRET_ACCESS_KEY` | Filebase secret key | - |
| `NEXT_PUBLIC_RPC_URL` | Blockchain RPC endpoint | `http://127.0.0.1:8545` |

## 🧪 Testing

### Smart Contract Tests
```bash
cd contracts
npm test
```

### Frontend Tests
```bash
cd chario
npm test
```

## 📦 Deployment

### Frontend Deployment (Vercel)

1. Connect your repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Smart Contract Deployment (Sepolia Testnet)

```bash
cd contracts
npm run deploy:sepolia
npm run verify:sepolia <contract-address>
```

### Blockchain Listener Deployment

Deploy to any Node.js hosting service (Railway, Render, etc.) with the environment variables configured.

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Modern component library
- **Wagmi** - React hooks for Ethereum
- **RainbowKit** - Wallet connection UI
- **Prisma** - Database ORM
- **Better Auth** - Authentication solution

### Backend
- **Hono.js** - Fast web framework
- **Ethers.js** - Ethereum library
- **Prisma** - Database ORM
- **PostgreSQL** - Database

### Blockchain
- **Solidity** - Smart contract language
- **Hardhat** - Development environment
- **OpenZeppelin** - Security contracts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues:

1. Check the [Issues](../../issues) page
2. Review the environment variables setup
3. Ensure all services are running
4. Check the console logs for errors

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [Shadcn/ui](https://ui.shadcn.com/)
- Web3 integration via [Wagmi](https://wagmi.sh/) and [RainbowKit](https://www.rainbowkit.com/)
- Smart contract development with [Hardhat](https://hardhat.org/)
